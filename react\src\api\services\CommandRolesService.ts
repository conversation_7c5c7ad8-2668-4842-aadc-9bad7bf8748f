import { CommandRoleCreateDto, CommandRoleDto } from '../models';
import { CommandRolesClient } from '../proxy';
import { httpClient } from '../httpClient';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://localhost:7079';

/**
 * Service wrapper per CommandRolesClient
 * Fornisce metodi statici per interagire con le API
 * Generato automaticamente - Non modificare manualmente
 */
export class CommandRolesService {
  private static commandRolesClient = new CommandRolesClient(
    API_BASE_URL,
    httpClient()
  );

  /**
   * getAll - Generato automaticamente
   * @returns Promise che risolve in CommandRoleDto[]
   */
  static getAll(signal?: AbortSignal): Promise<CommandRoleDto[]> {
    return this.commandRolesClient.getAll(signal);
  }

  /**
   * create - Generato automaticamente
   * @returns Promise che risolve in CommandRoleDto
   */
  static create(body: CommandRoleCreateDto | undefined, signal?: AbortSignal): Promise<CommandRoleDto> {
    return this.commandRolesClient.create(body, signal);
  }

  /**
   * getById - Generato automaticamente
   * @returns Promise che risolve in CommandRoleDto
   */
  static getById(id: string, signal?: AbortSignal): Promise<CommandRoleDto> {
    return this.commandRolesClient.getById(id, signal);
  }

  /**
   * update - Generato automaticamente
   * @returns Promise che risolve in void
   */
  static update(id: string, body: CommandRoleDto | undefined, signal?: AbortSignal): Promise<void> {
    return this.commandRolesClient.update(id, body, signal);
  }

  /**
   * delete - Generato automaticamente
   * @returns Promise che risolve in void
   */
  static delete(id: string, signal?: AbortSignal): Promise<void> {
    return this.commandRolesClient.delete(id, signal);
  }

  /**
   * getByUnitType - Generato automaticamente
   * @returns Promise che risolve in CommandRoleDto[]
   */
  static getByUnitType(unitTypeId: string, signal?: AbortSignal): Promise<CommandRoleDto[]> {
    return this.commandRolesClient.getByUnitType(unitTypeId, signal);
  }
}
