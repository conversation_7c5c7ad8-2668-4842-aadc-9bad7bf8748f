import { OrganizationUnitCommandAssignmentCreateDto, OrganizationUnitCommandAssignmentDto } from '../models';
import { OrganizationUnitCommandAssignmentsClient } from '../proxy';
import { httpClient } from '../httpClient';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://localhost:7079';

/**
 * Service wrapper per OrganizationUnitCommandAssignmentsClient
 * Fornisce metodi statici per interagire con le API
 * Generato automaticamente - Non modificare manualmente
 */
export class OrganizationUnitCommandAssignmentsService {
  private static organizationUnitCommandAssignmentsClient = new OrganizationUnitCommandAssignmentsClient(
    API_BASE_URL,
    httpClient()
  );

  /**
   * getAll - Generato automaticamente
   * @returns Promise che risolve in OrganizationUnitCommandAssignmentDto[]
   */
  static getAll(signal?: AbortSignal): Promise<OrganizationUnitCommandAssignmentDto[]> {
    return this.organizationUnitCommandAssignmentsClient.getAll(signal);
  }

  /**
   * create - Generato automaticamente
   * @returns Promise che risolve in OrganizationUnitCommandAssignmentDto
   */
  static create(body: OrganizationUnitCommandAssignmentCreateDto | undefined, signal?: AbortSignal): Promise<OrganizationUnitCommandAssignmentDto> {
    return this.organizationUnitCommandAssignmentsClient.create(body, signal);
  }

  /**
   * getById - Generato automaticamente
   * @returns Promise che risolve in OrganizationUnitCommandAssignmentDto
   */
  static getById(id: string, signal?: AbortSignal): Promise<OrganizationUnitCommandAssignmentDto> {
    return this.organizationUnitCommandAssignmentsClient.getById(id, signal);
  }

  /**
   * update - Generato automaticamente
   * @returns Promise che risolve in OrganizationUnitCommandAssignmentDto
   */
  static update(id: string, body: OrganizationUnitCommandAssignmentCreateDto | undefined, signal?: AbortSignal): Promise<OrganizationUnitCommandAssignmentDto> {
    return this.organizationUnitCommandAssignmentsClient.update(id, body, signal);
  }

  /**
   * delete - Generato automaticamente
   * @returns Promise che risolve in void
   */
  static delete(id: string, signal?: AbortSignal): Promise<void> {
    return this.organizationUnitCommandAssignmentsClient.delete(id, signal);
  }

  /**
   * getByOrganizationUnitId - Generato automaticamente
   * @returns Promise che risolve in OrganizationUnitCommandAssignmentDto[]
   */
  static getByOrganizationUnitId(organizationUnitId: string, signal?: AbortSignal): Promise<OrganizationUnitCommandAssignmentDto[]> {
    return this.organizationUnitCommandAssignmentsClient.getByOrganizationUnitId(organizationUnitId, signal);
  }

  /**
   * getByUserId - Generato automaticamente
   * @returns Promise che risolve in OrganizationUnitCommandAssignmentDto[]
   */
  static getByUserId(userId: string, signal?: AbortSignal): Promise<OrganizationUnitCommandAssignmentDto[]> {
    return this.organizationUnitCommandAssignmentsClient.getByUserId(userId, signal);
  }

  /**
   * getByCommandRoleId - Generato automaticamente
   * @returns Promise che risolve in OrganizationUnitCommandAssignmentDto[]
   */
  static getByCommandRoleId(commandRoleId: string, signal?: AbortSignal): Promise<OrganizationUnitCommandAssignmentDto[]> {
    return this.organizationUnitCommandAssignmentsClient.getByCommandRoleId(commandRoleId, signal);
  }
}
