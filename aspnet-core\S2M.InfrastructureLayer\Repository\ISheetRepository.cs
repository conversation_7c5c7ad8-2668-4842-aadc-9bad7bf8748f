using S2M.DomainModelLayer.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace S2M.InfrastructureLayer.Repository
{
    /// <summary>
    /// Interfaccia per il repository degli Sheet
    /// </summary>
    public interface ISheetRepository : IRepository<Sheet>
    {
        /// <summary>
        /// Ottiene tutti gli sheet di un flusso
        /// </summary>
        /// <param name="flowId">ID del flusso</param>
        /// <param name="trackChanges">Indica se tracciare le modifiche alle entità</param>
        /// <returns>Lista di sheet del flusso</returns>
        Task<IEnumerable<Sheet>> GetByFlowIdAsync(Guid flowId, bool trackChanges = false);
    }
} 