using Microsoft.EntityFrameworkCore;
using S2M.DomainModelLayer.Entities;
using S2M.InfrastructureLayer.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace S2M.InfrastructureLayer.Repository
{
    /// <summary>
    /// Implementazione del repository per l'entità Sheet
    /// </summary>
    public class SheetRepository : FullAuditedRepository<Sheet>, ISheetRepository
    {
        public SheetRepository(S2MDbContext context, IIdentityUser identityUser) : base(context, identityUser)
        {
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<Sheet>> GetByFlowIdAsync(Guid flowId, bool trackChanges = false)
        {
            var query = _dbSet
                .Include(s => s.FlowTypeSheet)
                .Include(s => s.History)
                .Include(s => s.Comments)
                .Where(s => s.FlowId == flowId && !s.IsDeleted);

            if (!trackChanges)
                query = query.AsNoTracking();

            return await query.ToListAsync();
        }
    }
} 