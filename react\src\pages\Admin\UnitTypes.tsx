import { useState, useEffect } from 'react';
import { Hi<PERSON><PERSON>, HiPencil, HiTrash, HiOutlineBadgeCheck } from 'react-icons/hi';
import styles from './UnitTypes.module.css';
import { AdminPageLayout } from '../../components/Admin/Layout/AdminPageLayout';
import { Button } from '../../components/Buttons/Button';
import { UnitTypeModal } from '../../components/Admin/UnitTypeModal/UnitTypeModal';
import { UnitTypeDeleteConfirm } from '../../components/Admin/UnitTypeDeleteConfirm/UnitTypeDeleteConfirm';
import { CommandRoleModal } from '../../components/Admin/CommandRoleModal/CommandRoleModal';
import { UnitTypeService } from '../../api/services';
import { UnitTypeDto, UnitTypeCreateDto, CommandRoleDto, UnitTypeEditCommandRolesDto } from '../../api/proxy';
import { Table } from '../../components/Table';

export const UnitTypes = () => {
  const [unitTypes, setUnitTypes] = useState<UnitTypeDto[]>([]);
  const [filteredUnitTypes, setFilteredUnitTypes] = useState<UnitTypeDto[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [selectedUnitType, setSelectedUnitType] = useState<UnitTypeDto | undefined>(undefined);
  
  const [isDeleteConfirmOpen, setIsDeleteConfirmOpen] = useState(false);
  const [unitTypeToDelete, setUnitTypeToDelete] = useState<UnitTypeDto | undefined>(undefined);
  
  const [isRoleModalOpen, setIsRoleModalOpen] = useState(false);
  const [unitTypeForRoles, setUnitTypeForRoles] = useState<UnitTypeDto | undefined>(undefined);

  // Carica i dati dall'API all'avvio
  useEffect(() => {
    fetchUnitTypes();
  }, []);

  const fetchUnitTypes = async () => {
    setIsLoading(true);
    try {
      const data = await UnitTypeService.getAllWithCommandRoles();
      setUnitTypes(data);
      setFilteredUnitTypes(data);
    } catch (error) {
      console.error('Errore nel recupero dei tipi di unità:', error);
      // Qui potresti mostrare una notifica di errore
    } finally {
      setIsLoading(false);
    }
  };

  // Filtra i tipi di unità in base alla query di ricerca
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredUnitTypes(unitTypes);
      return;
    }

    const query = searchQuery.toLowerCase();
    const filtered = unitTypes.filter(unitType =>
      (unitType.name?.toLowerCase().includes(query) || '') ||
      (unitType.description && unitType.description.toLowerCase().includes(query))
    );

    setFilteredUnitTypes(filtered);
  }, [searchQuery, unitTypes]);

  const handleAddUnitType = () => {
    setSelectedUnitType(undefined);
    setIsModalOpen(true);
  };

  const handleEditUnitType = (unitType: UnitTypeDto) => {
    setSelectedUnitType(unitType);
    setIsModalOpen(true);
  };

  const handleDeleteUnitType = (unitType: UnitTypeDto) => {
    setUnitTypeToDelete(unitType);
    setIsDeleteConfirmOpen(true);
  };

  const handleManageRoles = (unitType: UnitTypeDto) => {
    setUnitTypeForRoles(unitType);
    setIsRoleModalOpen(true);
  };

  const handleUnitTypeSubmit = async (formData: UnitTypeCreateDto) => {
    setIsLoading(true);
    try {
      if (selectedUnitType && selectedUnitType.id) {
        // Aggiorna un tipo esistente
        await UnitTypeService.update(selectedUnitType.id, formData);
      } else {
        // Crea un nuovo tipo
        await UnitTypeService.create(formData);
      }
      await fetchUnitTypes();
      setIsModalOpen(false);
    } catch (error) {
      console.error('Errore nel salvare il tipo di unità:', error);
      // Qui potresti mostrare una notifica di errore
    } finally {
      setIsLoading(false);
    }
  };

  const handleConfirmDelete = async () => {
    if (!unitTypeToDelete) return;
    
    setIsLoading(true);
    try {
      await UnitTypeService.delete(unitTypeToDelete.id ?? '');
      await fetchUnitTypes();
      setIsDeleteConfirmOpen(false);
    } catch (error) {
      console.error('Errore nell\'eliminare il tipo di unità:', error);
      // Qui potresti mostrare una notifica di errore
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveRoles = async (roles: CommandRoleDto[]) => {
    if (!unitTypeForRoles) return;
    
    setIsLoading(true);
    try {
      const utRoles = new UnitTypeEditCommandRolesDto({
        unitTypeId: unitTypeForRoles.id || '',
        commandRoles: roles
      });
      await UnitTypeService.updateCommandRoles(utRoles);
      await fetchUnitTypes();
      setIsRoleModalOpen(false);
    } catch (error) {
      console.error('Errore nel salvare i ruoli:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const renderColorSwatch = (unitType: UnitTypeDto) => (
    <div 
      className={styles.colorSwatch} 
      style={{ backgroundColor: unitType.color }}
      title={unitType.color}
    >
      <span>{unitType.name?.charAt(0) || ''}</span>
    </div>
  );

  // Toolbar con pulsante di aggiunta e campo di ricerca
  const toolbar = (
    <div className={styles.toolbar}>
      <div className={styles.search}>
        <input
          type="text"
          placeholder="Cerca tipi di unità..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
          className={styles.searchInput}
        />
      </div>
      <Button 
        type="primary"
        icon={<HiPlus size={20} />}
        onClick={handleAddUnitType}
      >
        Aggiungi tipo di unità
      </Button>
    </div>
  );

  return (
    <AdminPageLayout title="Tipi unità organizzative e figure gerarchiche" toolbar={toolbar}>
      <div className={styles.content}>
        <Table
          data={filteredUnitTypes}
          columns={[
            { key: 'name', header: 'Nome', sortable: true },
            { key: 'description', header: 'Descrizione' },
            {
              key: 'color',
              header: 'Colore',
              render: (unitType: UnitTypeDto) => renderColorSwatch(unitType)
            },
            {
              key: 'commandRoles',
              header: 'Ruoli gerarchici',
              render: (unitType: UnitTypeDto) => (
                <div className={styles.rolesBadge}>
                  {unitType.commandRoles?.length || 0}
                </div>
              )
            }
          ]}
          actions={[
            {
              icon: <HiPencil size={20} />,
              label: 'Modifica',
              onClick: handleEditUnitType,
              iconType: 'primary'
            },
            {
              icon: <HiOutlineBadgeCheck size={20} />,
              label: 'Gestisci ruoli di comando',
              onClick: handleManageRoles,
              iconType: 'primary'
            },
            {
              icon: <HiTrash size={20} />,
              label: 'Elimina',
              onClick: handleDeleteUnitType,
              iconType: 'error'
            }
          ]}
          emptyMessage="Nessun tipo di unità trovato"
          rowKey="id"
          isLoading={isLoading}
        />
      </div>

      <UnitTypeModal
        isOpen={isModalOpen}
        onClose={() => setIsModalOpen(false)}
        onSubmit={handleUnitTypeSubmit}
        unitType={selectedUnitType}
      />

      <UnitTypeDeleteConfirm
        isOpen={isDeleteConfirmOpen}
        onClose={() => setIsDeleteConfirmOpen(false)}
        onConfirm={handleConfirmDelete}
        unitType={unitTypeToDelete}
      />

      <CommandRoleModal
        isOpen={isRoleModalOpen}
        onClose={() => setIsRoleModalOpen(false)}
        onSave={handleSaveRoles}
        unitType={unitTypeForRoles as UnitTypeDto}
      />
    </AdminPageLayout>
  );
};