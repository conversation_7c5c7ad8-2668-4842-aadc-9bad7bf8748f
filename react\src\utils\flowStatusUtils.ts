import { FlowStatus } from '../api/enums';

/**
 * Mapping degli status dei flussi con le etichette in italiano
 */
export const FlowStatusLabels: Record<FlowStatus, string> = {
  [FlowStatus._0]: 'Bozza',           // Draft
  [FlowStatus._1]: 'Stesura',         // Writing  
  [FlowStatus._2]: 'Pre-Approvazione', // PreApproval
  [FlowStatus._3]: 'Pre-Approvato',   // PreApproved
  [FlowStatus._4]: 'Coordinamento',   // Coordination
  [FlowStatus._5]: 'Revisione',       // Revision
  [FlowStatus._6]: 'Approvazione',    // Approval
  [FlowStatus._7]: 'Pubblicato',      // Published
  [FlowStatus._8]: 'Rifiutato',       // Rejected
  [FlowStatus._9]: 'Annullato'        // Canceled
};

/**
 * Classi CSS per colorare gli status in base al loro stato
 */
export const FlowStatusClasses: Record<FlowStatus, string> = {
  [FlowStatus._0]: 'statusDraft',
  [FlowStatus._1]: 'statusWriting',
  [FlowStatus._2]: 'statusPreApproval',
  [FlowStatus._3]: 'statusPreApproved',
  [FlowStatus._4]: 'statusCoordination',
  [FlowStatus._5]: 'statusRevision',
  [FlowStatus._6]: 'statusApproval',
  [FlowStatus._7]: 'statusPublished',
  [FlowStatus._8]: 'statusRejected',
  [FlowStatus._9]: 'statusCanceled'
};

/**
 * Ottiene l'etichetta in italiano per uno status di flusso
 */
export const getFlowStatusLabel = (status: FlowStatus): string => {
  return FlowStatusLabels[status] || 'Status sconosciuto';
};

/**
 * Ottiene la classe CSS per uno status di flusso
 */
export const getFlowStatusClass = (status: FlowStatus): string => {
  return FlowStatusClasses[status] || 'statusUnknown';
};

/**
 * Mapping degli stili inline per gli status dei flussi
 */
const FlowStatusStyles: Record<FlowStatus, React.CSSProperties> = {
  [FlowStatus._0]: {
    backgroundColor: '#f3f4f6',
    color: '#6b7280',
    borderColor: '#d1d5db'
  },
  [FlowStatus._1]: {
    backgroundColor: '#fef3c7',
    color: '#d97706',
    borderColor: '#fbbf24'
  },
  [FlowStatus._2]: {
    backgroundColor: '#fde68a',
    color: '#92400e',
    borderColor: '#f59e0b'
  },
  [FlowStatus._3]: {
    backgroundColor: '#d1fae5',
    color: '#065f46',
    borderColor: '#10b981'
  },
  [FlowStatus._4]: {
    backgroundColor: '#dbeafe',
    color: '#1e40af',
    borderColor: '#3b82f6'
  },
  [FlowStatus._5]: {
    backgroundColor: '#ede9fe',
    color: '#6d28d9',
    borderColor: '#8b5cf6'
  },
  [FlowStatus._6]: {
    backgroundColor: '#fdf4ff',
    color: '#a21caf',
    borderColor: '#d946ef'
  },
  [FlowStatus._7]: {
    backgroundColor: '#dcfce7',
    color: '#166534',
    borderColor: '#22c55e'
  },
  [FlowStatus._8]: {
    backgroundColor: '#fee2e2',
    color: '#dc2626',
    borderColor: '#f87171'
  },
  [FlowStatus._9]: {
    backgroundColor: '#f3f4f6',
    color: '#374151',
    borderColor: '#9ca3af'
  }
};

/**
 * Ottiene gli stili inline per uno status di flusso
 */
export const getFlowStatusStyles = (status: FlowStatus): React.CSSProperties => {
  const baseStyles: React.CSSProperties = {
    padding: '4px 8px',
    borderRadius: '12px',
    fontSize: '12px',
    fontWeight: '500',
    textAlign: 'center',
    border: '1px solid',
    display: 'inline-block'
  };

  const statusStyles = FlowStatusStyles[status] || {
    backgroundColor: '#f9fafb',
    color: '#6b7280',
    borderColor: '#d1d5db'
  };

  return { ...baseStyles, ...statusStyles };
};

/**
 * Verifica se uno status indica un flusso attivo (in lavorazione)
 */
export const isActiveFlow = (status: FlowStatus): boolean => {
  return [
    FlowStatus._1, // Stesura
    FlowStatus._2, // Pre-Approvazione
    FlowStatus._3, // Pre-Approvato
    FlowStatus._4, // Coordinamento
    FlowStatus._5, // Revisione
    FlowStatus._6  // Approvazione
  ].includes(status);
};

/**
 * Verifica se uno status indica un flusso completato (finito)
 */
export const isCompletedFlow = (status: FlowStatus): boolean => {
  return [
    FlowStatus._7, // Pubblicato
    FlowStatus._8, // Rifiutato
    FlowStatus._9  // Annullato
  ].includes(status);
};

/**
 * Verifica se uno status indica una bozza
 */
export const isDraftFlow = (status: FlowStatus): boolean => {
  return status === FlowStatus._0;
}; 