import { useState, useEffect } from 'react';
import { OrganizationUnitDto, ProjectDto } from '../api/models';
import { OrganizationUnitsService } from '../api/services';

interface ProjectOrgUnitRole {
  id: string;
  projectId: string;
  organizationUnitId: string;
  unitRole: 'Owner' | 'Collaborator';
  organizationUnitName: string;
  organizationUnitEmail: string;
}

export const useProjectOrganizationUnits = (project?: ProjectDto) => {
  const [organizationUnits, setOrganizationUnits] = useState<OrganizationUnitDto[]>([]);
  const [projectRoles, setProjectRoles] = useState<ProjectOrgUnitRole[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Carica tutte le unità organizzative attive
  useEffect(() => {
    const fetchOrganizationUnits = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const units = await OrganizationUnitsService.getActive();
        if (Array.isArray(units)) {
          setOrganizationUnits(units);
        }
      } catch (err) {
        setError('Errore nel caricamento delle unità organizzative');
        console.error('Error fetching organization units:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrganizationUnits();
  }, []);

  // Carica i ruoli del progetto specifico quando disponibile
  useEffect(() => {
    if (!project?.id) {
      setProjectRoles([]);
      return;
    }

    const fetchProjectRoles = async () => {
      try {
        setIsLoading(true);
        setError(null);
        
        // Utilizziamo i dati reali dal ProjectDto
        const roles: ProjectOrgUnitRole[] = [];
        
        // Aggiungi l'owner se presente
        if (project.owner) {
          roles.push({
            id: `owner-${project.owner.id}`,
            projectId: project.id!,
            organizationUnitId: project.owner.id!,
            unitRole: 'Owner',
            organizationUnitName: project.owner.name || '',
            organizationUnitEmail: project.owner.email || ''
          });
        }
        
        // Aggiungi i collaboratori se presenti
        if (project.collaborators && Array.isArray(project.collaborators)) {
          project.collaborators.forEach((collaborator, index) => {
            roles.push({
              id: `collaborator-${collaborator.id}-${index}`,
              projectId: project.id!,
              organizationUnitId: collaborator.id!,
              unitRole: 'Collaborator',
              organizationUnitName: collaborator.name || '',
              organizationUnitEmail: collaborator.email || ''
            });
          });
        }
        
        setProjectRoles(roles);
        
      } catch (err) {
        setError('Errore nel caricamento dei ruoli del progetto');
        console.error('Error fetching project roles:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchProjectRoles();
  }, [project]);

  // Utility functions
  const getOwner = () => projectRoles.find(role => role.unitRole === 'Owner');
  
  const getCollaborators = () => projectRoles.filter(role => role.unitRole === 'Collaborator');
  
  const getOrganizationUnitById = (id: string) => 
    organizationUnits.find(unit => unit.id === id);

  const getAvailableUnitsForCollaboration = (excludeOwnerId?: string) =>
    organizationUnits.filter(unit => unit.id !== excludeOwnerId);

  // Helper per ottenere i nomi delle unità organizzative
  const getOwnerName = () => {
    const owner = getOwner();
    return owner?.organizationUnitName || '';
  };

  const getCollaboratorNames = () => {
    const collaborators = getCollaborators();
    return collaborators.map(c => c.organizationUnitName);
  };

  return {
    organizationUnits,
    projectRoles,
    isLoading,
    error,
    getOwner,
    getCollaborators,
    getOrganizationUnitById,
    getAvailableUnitsForCollaboration,
    getOwnerName,
    getCollaboratorNames
  };
}; 