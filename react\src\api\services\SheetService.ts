import { CreateCommentWithContentUpdateRequest, DeleteCommentWithContentUpdateRequest, SheetCommentDto, SheetCreateDto, SheetDto, SheetUpdateWithCommentsDto, SheetUpdateWithCommentsResponseDto } from '../models';
import { SheetClient } from '../proxy';
import { httpClient } from '../httpClient';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://localhost:7079';

/**
 * Service wrapper per SheetClient
 * Fornisce metodi statici per interagire con le API
 * Generato automaticamente - Non modificare manualmente
 */
export class SheetService {
  private static sheetClient = new SheetClient(
    API_BASE_URL,
    httpClient()
  );

  /**
   * getAll - Generato automaticamente
   * @returns Promise che risolve in SheetDto[]
   */
  static getAll(signal?: AbortSignal): Promise<SheetDto[]> {
    return this.sheetClient.getAll(signal);
  }

  /**
   * create - Generato automaticamente
   * @returns Promise che risolve in SheetDto
   */
  static create(body: SheetCreateDto | undefined, signal?: AbortSignal): Promise<SheetDto> {
    return this.sheetClient.create(body, signal);
  }

  /**
   * getById - Generato automaticamente
   * @returns Promise che risolve in SheetDto
   */
  static getById(id: string, signal?: AbortSignal): Promise<SheetDto> {
    return this.sheetClient.getById(id, signal);
  }

  /**
   * update - Generato automaticamente
   * @returns Promise che risolve in void
   */
  static update(id: string, body: SheetDto | undefined, signal?: AbortSignal): Promise<void> {
    return this.sheetClient.update(id, body, signal);
  }

  /**
   * delete - Generato automaticamente
   * @returns Promise che risolve in void
   */
  static delete(id: string, signal?: AbortSignal): Promise<void> {
    return this.sheetClient.delete(id, signal);
  }

  /**
   * getByFlowId - Generato automaticamente
   * @returns Promise che risolve in SheetDto[]
   */
  static getByFlowId(flowId: string, signal?: AbortSignal): Promise<SheetDto[]> {
    return this.sheetClient.getByFlowId(flowId, signal);
  }

  /**
   * getContent - Generato automaticamente
   * @returns Promise che risolve in string
   */
  static getContent(id: string, signal?: AbortSignal): Promise<string> {
    return this.sheetClient.getContent(id, signal);
  }

  /**
   * updateContentWithComments - Generato automaticamente
   * @returns Promise che risolve in SheetUpdateWithCommentsResponseDto
   */
  static updateContentWithComments(id: string, body: SheetUpdateWithCommentsDto | undefined, signal?: AbortSignal): Promise<SheetUpdateWithCommentsResponseDto> {
    return this.sheetClient.updateContentWithComments(id, body, signal);
  }

  /**
   * createCommentWithContentUpdate - Generato automaticamente
   * @returns Promise che risolve in SheetCommentDto
   */
  static createCommentWithContentUpdate(id: string, body: CreateCommentWithContentUpdateRequest | undefined, signal?: AbortSignal): Promise<SheetCommentDto> {
    return this.sheetClient.createCommentWithContentUpdate(id, body, signal);
  }

  /**
   * deleteCommentWithContentUpdate - Generato automaticamente
   * @returns Promise che risolve in void
   */
  static deleteCommentWithContentUpdate(id: string, commentId: string, body: DeleteCommentWithContentUpdateRequest | undefined, signal?: AbortSignal): Promise<void> {
    return this.sheetClient.deleteCommentWithContentUpdate(id, commentId, body, signal);
  }
}
