.modalTitle {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.2rem;
  font-weight: 600;
  color: var(--primary-color);
}

.titleIcon {
  font-size: 1.4rem;
}

.modalContent {
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-height: 70vh;
  overflow-y: auto;
  padding: 16px 24px 24px 24px;
}

.loading {
  text-align: center;
  padding: 32px;
  color: var(--text-secondary);
}

.section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.sectionTitle {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
  padding-bottom: 8px;
  border-bottom: 2px solid var(--border-color);
}

.contributorsList,
.availableUsersList {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
  padding: 4px;
}

.contributorCard,
.userCard {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--bg-secondary);
  transition: all 0.2s ease;
}

.contributorCard:hover,
.userCard:hover {
  border-color: var(--primary-color);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.contributorInfo,
.userInfo {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
}

.contributorHeader {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 12px;
}

.contributorName {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.contributorStatus {
  flex-shrink: 0;
}

.statusBadgeCompleted {
  background-color: #d4edda;
  color: #155724;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid #c3e6cb;
}

.statusBadgeActive {
  background-color: #fff3cd;
  color: #856404;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid #ffeaa7;
}

.statusBadgeNew {
  background-color: #e3f2fd;
  color: #1565c0;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  border: 1px solid #bbdefb;
}

.contributorDetails {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.contributorEmail {
  font-size: 12px;
  color: #666;
}

.contributorUnit {
  font-size: 12px;
  color: #888;
}

.acknowledgeDate {
  font-size: 11px;
  color: #155724;
  font-style: italic;
  margin-top: 2px;
}

.userName {
  font-weight: 500;
  color: #333;
  font-size: 14px;
}

.userDetails {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.userEmail {
  font-size: 12px;
  color: #666;
}

.userUnit {
  font-size: 12px;
  color: #888;
  font-style: italic;
}

.removeButton,
.addButton {
  min-width: 32px;
  min-height: 32px;
  padding: 6px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.removeButton:hover {
  background-color: var(--danger-color);
  color: white;
}

.addButton:hover {
  background-color: var(--primary-color-dark);
}

.searchContainer {
  margin-bottom: 16px;
}

.searchInputWrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.searchIcon {
  position: absolute;
  left: 12px;
  color: var(--text-tertiary);
  font-size: 1.1rem;
  z-index: 1;
}

.searchInput {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  font-size: 0.9rem;
  background-color: var(--bg-primary);
  transition: border-color 0.2s ease;
}

.searchInput:focus {
  outline: none;
  border-color: var(--primary-color);
  box-shadow: 0 0 0 3px rgba(var(--primary-color-rgb), 0.1);
}

.searchInput::placeholder {
  color: var(--text-tertiary);
}

.emptyState {
  text-align: center;
  padding: 24px;
  color: var(--text-secondary);
  font-style: italic;
  background-color: var(--bg-tertiary);
  border-radius: 8px;
  border: 1px dashed var(--border-color);
}

.modalActions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  padding-top: 16px;
  border-top: 1px solid var(--border-color);
  margin-top: 8px;
}

.modalActions button {
  min-width: 120px;
}

/* Scrollbar styling per le liste */
.contributorsList::-webkit-scrollbar,
.availableUsersList::-webkit-scrollbar {
  width: 6px;
}

.contributorsList::-webkit-scrollbar-track,
.availableUsersList::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 3px;
}

.contributorsList::-webkit-scrollbar-thumb,
.availableUsersList::-webkit-scrollbar-thumb {
  background: var(--border-color);
  border-radius: 3px;
}

.contributorsList::-webkit-scrollbar-thumb:hover,
.availableUsersList::-webkit-scrollbar-thumb:hover {
  background: var(--text-tertiary);
} 