import { OrganizationUnitCreateDto, OrganizationUnitDto } from '../models';
import { OrganizationUnitsClient } from '../proxy';
import { httpClient } from '../httpClient';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://localhost:7079';

/**
 * Service wrapper per OrganizationUnitsClient
 * Fornisce metodi statici per interagire con le API
 * Generato automaticamente - Non modificare manualmente
 */
export class OrganizationUnitsService {
  private static organizationUnitsClient = new OrganizationUnitsClient(
    API_BASE_URL,
    httpClient()
  );

  /**
   * getAll - Generato automaticamente
   * @returns Promise che risolve in OrganizationUnitDto[]
   */
  static getAll(signal?: AbortSignal): Promise<OrganizationUnitDto[]> {
    return this.organizationUnitsClient.getAll(signal);
  }

  /**
   * create - Generato automaticamente
   * @returns Promise che risolve in OrganizationUnitDto
   */
  static create(body: OrganizationUnitCreateDto | undefined, signal?: AbortSignal): Promise<OrganizationUnitDto> {
    return this.organizationUnitsClient.create(body, signal);
  }

  /**
   * getById - Generato automaticamente
   * @returns Promise che risolve in OrganizationUnitDto
   */
  static getById(id: string, signal?: AbortSignal): Promise<OrganizationUnitDto> {
    return this.organizationUnitsClient.getById(id, signal);
  }

  /**
   * update - Generato automaticamente
   * @returns Promise che risolve in OrganizationUnitDto
   */
  static update(id: string, body: OrganizationUnitCreateDto | undefined, signal?: AbortSignal): Promise<OrganizationUnitDto> {
    return this.organizationUnitsClient.update(id, body, signal);
  }

  /**
   * delete - Generato automaticamente
   * @returns Promise che risolve in void
   */
  static delete(id: string, signal?: AbortSignal): Promise<void> {
    return this.organizationUnitsClient.delete(id, signal);
  }

  /**
   * getChildren - Generato automaticamente
   * @returns Promise che risolve in OrganizationUnitDto[]
   */
  static getChildren(parentId: string, signal?: AbortSignal): Promise<OrganizationUnitDto[]> {
    return this.organizationUnitsClient.getChildren(parentId, signal);
  }

  /**
   * getByUnitType - Generato automaticamente
   * @returns Promise che risolve in OrganizationUnitDto[]
   */
  static getByUnitType(unitTypeId: string, signal?: AbortSignal): Promise<OrganizationUnitDto[]> {
    return this.organizationUnitsClient.getByUnitType(unitTypeId, signal);
  }

  /**
   * getActive - Generato automaticamente
   * @returns Promise che risolve in OrganizationUnitDto[]
   */
  static getActive(signal?: AbortSignal): Promise<OrganizationUnitDto[]> {
    return this.organizationUnitsClient.getActive(signal);
  }
}
