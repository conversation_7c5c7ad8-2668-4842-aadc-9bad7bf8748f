/**
 * Enum per le azioni che un utente può svolgere nell'applicativo
 */
export enum UserAction {
  // Document Flow - Gestione flusso documenti
  VIEW_PENDING_DOCUMENTS = 'DocumentFlow.ViewPendingDocuments',
  VIEW_OPEN_DOCUMENTS = 'DocumentFlow.ViewOpenDocuments',
  EDIT_FLOW_ATTRIBUTES = 'DocumentFlow.EditFlowAttributes',
  
  // Content Management - Gestione contenuti
  SAVE_SECTIONS = 'ContentManagement.SaveSections',
  COMMENT = 'ContentManagement.Comment',
  DELETE = 'ContentManagement.Delete',
  CANCEL = 'ContentManagement.Cancel',
  SAVE = 'ContentManagement.Save',
  
  // Document Publishing - Pubblicazione documenti
  PUBLISH_DRAFT = 'DocumentPublishing.PublishDraft',
  SEND_FOR_PRE_APPROVAL = 'DocumentPublishing.SendForPreApproval',
  FORWARD_TO_SUPERVISOR = 'DocumentPublishing.ForwardToSupervisor',
  SEND_IN_COORDINATION = 'DocumentPublishing.SendInCoordination',
  
  // Communication - Comunicazione
  REQUEST_TO_SPEAK = 'Communication.RequestToSpeak',
  APPROVE = 'Communication.Approve',
  DISAPPROVE = 'Communication.Disapprove',
  CLOSE_USER_COORDINATION = 'Communication.CloseUserCoordination',
  CLOSE_UNIT_COORDINATION = 'Communication.CloseUnitCoordination',
  CLOSE_USER_CONTRIBUTION = 'Communication.CloseUserContribution',
  
  // Selection - Selezione ruoli e responsabilità
  SELECT_CONTRIBUTORS = 'Selection.SelectContributors',
  SELECT_COORDINATION_UNIT = 'Selection.SelectCoordinationUnit',
  SELECT_COORDINATION_EXPIRATION_DATE = 'Selection.SelectCoordinationExpirationDate',
  SELECT_COORDINATORS = 'Selection.SelectCoordinators',
  
  // Utility - Utilità
  MANAGE_ATTACHMENTS = 'Utility.ManageAttachments',
  VIEW_HISTORY = 'Utility.ViewHistory'
}

/**
 * Etichette in italiano per le azioni utente (per la UI)
 */
export const UserActionLabels: Record<UserAction, string> = {
  [UserAction.VIEW_PENDING_DOCUMENTS]: 'Visualizza flusso in documenti da trattare',
  [UserAction.VIEW_OPEN_DOCUMENTS]: 'Visualizza flusso in documenti aperti',
  [UserAction.EDIT_FLOW_ATTRIBUTES]: 'Modifica attributi flusso',
  [UserAction.SAVE_SECTIONS]: 'Salva sezioni',
  [UserAction.COMMENT]: 'Commenta',
  [UserAction.DELETE]: 'Elimina',
  [UserAction.CANCEL]: 'Annulla',
  [UserAction.SAVE]: 'Salva',
  [UserAction.PUBLISH_DRAFT]: 'Pubblica bozza',
  [UserAction.SEND_FOR_PRE_APPROVAL]: 'Invia in pre-approvazione',
  [UserAction.FORWARD_TO_SUPERVISOR]: 'Inoltra a superiore',
  [UserAction.SEND_IN_COORDINATION]: 'Invia in Coordinamento',
  [UserAction.REQUEST_TO_SPEAK]: 'Prego parlare',
  [UserAction.APPROVE]: 'Approvo',
  [UserAction.DISAPPROVE]: 'Non-Approvo',
  [UserAction.CLOSE_USER_COORDINATION]: 'Chiudi coordinamento utente',
  [UserAction.CLOSE_UNIT_COORDINATION]: 'Chiudi coordinamento unità',
  [UserAction.CLOSE_USER_CONTRIBUTION]: 'Conferma fine contribuzione',
  [UserAction.SELECT_CONTRIBUTORS]: 'Scegli Contributori',
  [UserAction.SELECT_COORDINATION_UNIT]: 'Scegli Unità Coordinamento',
  [UserAction.SELECT_COORDINATION_EXPIRATION_DATE]: 'Scegli Data Scadenza Coordinamento',
  [UserAction.SELECT_COORDINATORS]: 'Scegli Coordinatori',
  [UserAction.MANAGE_ATTACHMENTS]: 'Allegati',
  [UserAction.VIEW_HISTORY]: 'Storico'
};

/**
 * Categorie delle azioni per una migliore organizzazione
 */
export enum ActionCategory {
  DOCUMENT_FLOW = 'DocumentFlow',
  CONTENT_MANAGEMENT = 'ContentManagement',
  DOCUMENT_PUBLISHING = 'DocumentPublishing',
  COMMUNICATION = 'Communication',
  SELECTION = 'Selection',
  UTILITY = 'Utility'
}

/**
 * Mapping delle azioni alle loro categorie
 */
export const ActionCategoryMap: Record<UserAction, ActionCategory> = {
  [UserAction.VIEW_PENDING_DOCUMENTS]: ActionCategory.DOCUMENT_FLOW,
  [UserAction.VIEW_OPEN_DOCUMENTS]: ActionCategory.DOCUMENT_FLOW,
  [UserAction.EDIT_FLOW_ATTRIBUTES]: ActionCategory.DOCUMENT_FLOW,
  [UserAction.SAVE_SECTIONS]: ActionCategory.CONTENT_MANAGEMENT,
  [UserAction.COMMENT]: ActionCategory.CONTENT_MANAGEMENT,
  [UserAction.DELETE]: ActionCategory.CONTENT_MANAGEMENT,
  [UserAction.CANCEL]: ActionCategory.CONTENT_MANAGEMENT,
  [UserAction.SAVE]: ActionCategory.CONTENT_MANAGEMENT,
  [UserAction.PUBLISH_DRAFT]: ActionCategory.DOCUMENT_PUBLISHING,
  [UserAction.SEND_FOR_PRE_APPROVAL]: ActionCategory.DOCUMENT_PUBLISHING,
  [UserAction.FORWARD_TO_SUPERVISOR]: ActionCategory.DOCUMENT_PUBLISHING,
  [UserAction.SEND_IN_COORDINATION]: ActionCategory.DOCUMENT_PUBLISHING,
  [UserAction.REQUEST_TO_SPEAK]: ActionCategory.COMMUNICATION,
  [UserAction.APPROVE]: ActionCategory.COMMUNICATION,
  [UserAction.DISAPPROVE]: ActionCategory.COMMUNICATION,
  [UserAction.CLOSE_USER_COORDINATION]: ActionCategory.COMMUNICATION,
  [UserAction.CLOSE_UNIT_COORDINATION]: ActionCategory.COMMUNICATION,
  [UserAction.CLOSE_USER_CONTRIBUTION]: ActionCategory.COMMUNICATION,
  [UserAction.SELECT_CONTRIBUTORS]: ActionCategory.SELECTION,
  [UserAction.SELECT_COORDINATION_UNIT]: ActionCategory.SELECTION,
  [UserAction.SELECT_COORDINATION_EXPIRATION_DATE]: ActionCategory.SELECTION,
  [UserAction.SELECT_COORDINATORS]: ActionCategory.SELECTION,
  [UserAction.MANAGE_ATTACHMENTS]: ActionCategory.UTILITY,
  [UserAction.VIEW_HISTORY]: ActionCategory.UTILITY
};

/**
 * Utility functions per lavorare con le azioni utente
 */
export class UserActionUtils {
  /**
   * Ottiene tutte le azioni di una specifica categoria
   */
  static getActionsByCategory(category: ActionCategory): UserAction[] {
    return Object.entries(ActionCategoryMap)
      .filter(([, actionCategory]) => actionCategory === category)
      .map(([action]) => action as UserAction);
  }
  
  /**
   * Ottiene la categoria di un'azione
   */
  static getCategoryByAction(action: UserAction): ActionCategory {
    return ActionCategoryMap[action];
  }
  
  /**
   * Ottiene l'etichetta italiana di un'azione
   */
  static getActionLabel(action: UserAction): string {
    return UserActionLabels[action];
  }
  
  /**
   * Ottiene tutte le azioni disponibili
   */
  static getAllActions(): UserAction[] {
    return Object.values(UserAction);
  }
} 