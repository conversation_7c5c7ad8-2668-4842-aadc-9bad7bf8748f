using S2M.ApplicationLayer.Dto;
using S2M.ApplicationLayer.Services.Interfaces;
using S2M.DomainModelLayer.Constants;
using S2M.DomainModelLayer.Entities;
using S2M.DomainModelLayer.Enums;
using S2M.InfrastructureLayer.Repository;
using S2M.InfrastructureLayer.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Action = S2M.DomainModelLayer.Constants.Action;

namespace S2M.ApplicationLayer.Services.Implementations
{
    /// <summary>
    /// Implementazione del servizio per i permessi sui flussi documentali
    /// </summary>
    public class FlowPermissionService : IFlowPermissionService
    {
        private readonly IFlowRepository _flowRepository;
        private readonly IFlowUserRepository _flowUserRepository;
        private readonly ICurrentUserService _currentUserService;
        private readonly IMappingService _mappingService;
        private readonly IUserCacheService _userCacheService;

        /// <summary>
        /// Costruttore
        /// </summary>
        public FlowPermissionService(
            IFlowRepository flowRepository,
            IFlowUserRepository flowUserRepository,
            ICurrentUserService currentUserService,
            IMappingService mappingService,
            IUserCacheService userCacheService)
        {
            _flowRepository = flowRepository;
            _flowUserRepository = flowUserRepository;
            _currentUserService = currentUserService;
            _mappingService = mappingService;
            _userCacheService = userCacheService;
        }

        /// <summary>
        /// Converte una stringa in un'istanza di Action
        /// </summary>
        private Action ConvertToAction(string actionName)
        {
            // Cerca l'azione in tutte le classi statiche di UserActions
            var allActions = typeof(UserActions)
                .GetNestedTypes()
                .SelectMany(t => t.GetFields())
                .Where(f => f.FieldType == typeof(Action))
                .Select(f => (Action)f.GetValue(null))
                .FirstOrDefault(a => a.Name == actionName);

            if (allActions == null)
            {
                throw new ArgumentException($"Azione '{actionName}' non trovata", nameof(actionName));
            }

            return allActions;
        }

        /// <summary>
        /// Ottiene un utente per ID e lo converte in CurrentUserDto
        /// </summary>
        private async Task<CurrentUserDto> GetUserByIdAsync(Guid userId)
        {
            // Per ora, dobbiamo usare il repository direttamente poiché IUserCacheService
            // supporta solo la ricerca per username
            // TODO: Estendere IUserCacheService per supportare la ricerca per ID

            // Utilizziamo il servizio di mapping per ottenere l'utente
            // Questo è un workaround temporaneo - idealmente dovremmo avere un repository utenti
            var userEntity = await _flowRepository.GetUserByIdAsync(userId);
            if (userEntity == null)
            {
                return null;
            }

            // Convertiamo l'entità User in CurrentUserDto
            // Estraiamo ruoli e permessi dall'entità
            var roles = userEntity.UserRoles?.Where(ur => !ur.IsDeleted)
                .Select(ur => ur.Role.Name) ?? new List<string>();

            var permissions = userEntity.UserRoles?.Where(ur => !ur.IsDeleted)
                .SelectMany(ur => ur.Role.RolePermissions)
                .Where(rp => !rp.IsDeleted)
                .Select(rp => rp.Permission.Name)
                .Distinct() ?? new List<string>();

            return new CurrentUserDto
            {
                Id = userEntity.Id,
                Username = userEntity.Username,
                FirstName = userEntity.FirstName,
                LastName = userEntity.LastName,
                Rank = userEntity.Rank,
                Email = userEntity.Email,
                Roles = roles,
                Permissions = permissions,
                OrganizationUnitId = userEntity.OrganizationUnitId
            };
        }

        /// <summary>
        /// Ottiene le azioni disponibili per un utente specifico in un flusso
        /// </summary>
        public async Task<FlowAvailableActionsDto> GetAvailableActionsAsync(Guid flowId, Guid? userId = null)
        {
            // Ottieni il flusso per verificare lo status
            var flowEntity = await _flowRepository.GetByIdAsync(flowId, trackChanges: false);
            if (flowEntity == null)
            {
                throw new ArgumentException("Flusso non trovato", nameof(flowId));
            }
            
            var flow = _mappingService.Map<Flow, FlowDto>(flowEntity);

            // Determina l'utente target in base all'input
            CurrentUserDto targetUser;
            Guid targetUserId;
            
            if (userId.HasValue)
            {
                // Se è stato specificato un userId, usa quello
                targetUserId = userId.Value;
                targetUser = await _userCacheService.GetUserAsync(targetUserId);
                if (targetUser == null)
                {
                    throw new ArgumentException($"Utente con ID {targetUserId} non trovato", nameof(userId));
                }
            }
            else
            {
                // Altrimenti usa l'utente corrente
                targetUser = await _currentUserService.GetCurrentUserAsync();
                if (targetUser == null)
                {
                    throw new UnauthorizedAccessException("Impossibile determinare l'utente corrente");
                }
                targetUserId = targetUser.Id;
            }

            // Ottieni i sottoruoli dell'utente per questo flusso
            var userRoles = await _flowUserRepository.GetByFlowIdAsync(flowId, trackChanges: false);
            var userFlowRoles = userRoles.Where(ur => ur.UserId == targetUserId && !ur.IsDeleted).ToList();

            var allAvailableActions = new List<string>();

            //Se sono admin posso eseguire tutte le azioni
            if (targetUser.HasPermission(Permissions.Flows.AllowAll))
            {
                // Ottieni tutte le azioni disponibili per lo status corrente (indipendentemente dal ruolo)
                var allActionsForStatus = FlowPermissionMatrix.ActionsByStatusAndRole[flow.FlowStatus].Keys.ToArray();

                return new FlowAvailableActionsDto
                {
                    FlowId = flowId,
                    UserId = targetUserId,
                    FlowStatus = flow.FlowStatus,
                    UserRoles = new FlowUserRoleType[0], // Admin bypass dei ruoli specifici
                    AvailableActions = allActionsForStatus.Select(x => x.Name).ToArray(),
                    ActionDetails = allActionsForStatus.Select(action => new ActionDetailDto
                    {
                        Action = action.Name,
                        AuthorizedRoles = FlowPermissionMatrix.GetAuthorizedRolesForAction(flow.FlowStatus, action)
                    }).ToArray(),
                    Message = "Utente con permessi amministrativi - accesso completo"
                };
            }

            // Per ogni ruolo dell'utente, ottieni le azioni disponibili
            foreach (var userRole in userFlowRoles)
            {
                var actionsForRole = FlowPermissionMatrix.GetAvailableActions(flow.FlowStatus, userRole.Role).Select(x => x.Name);
                allAvailableActions.AddRange(actionsForRole);
            }

            // Rimuovi duplicati
            var uniqueActions = allAvailableActions.Distinct().ToArray();

            return new FlowAvailableActionsDto
            {
                FlowId = flowId,
                UserId = targetUserId,
                FlowStatus = flow.FlowStatus,
                UserRoles = userFlowRoles.Select(ur => ur.Role).ToArray(),
                AvailableActions = uniqueActions,
                ActionDetails = uniqueActions.Select(action => new ActionDetailDto
                {
                    Action = action,
                    AuthorizedRoles = FlowPermissionMatrix.GetAuthorizedRolesForAction(flow.FlowStatus, ConvertToAction(action))
                }).ToArray()
            };
        }

        /// <summary>
        /// Verifica se un utente può eseguire una specifica azione su un flusso
        /// </summary>
        public async Task<CanExecuteActionDto> CanExecuteActionAsync(Guid flowId, string action, Guid? userId = null)
        {
            if (action == null)
            {
                throw new ArgumentException("L'azione deve essere specificata", nameof(action));
            }

            // Ottieni il flusso per verificare lo status
            var flowEntity = await _flowRepository.GetByIdAsync(flowId, trackChanges: false);
            if (flowEntity == null)
            {
                throw new ArgumentException("Flusso non trovato", nameof(flowId));
            }
            
            var flow = _mappingService.Map<Flow, FlowDto>(flowEntity);

            // Determina l'utente target in base all'input
            CurrentUserDto targetUser;
            Guid targetUserId;
            
            if (userId.HasValue)
            {
                // Se è stato specificato un userId, usa quello
                targetUserId = userId.Value;
                targetUser = await _userCacheService.GetUserAsync(targetUserId);
                if (targetUser == null)
                {
                    throw new ArgumentException($"Utente con ID {targetUserId} non trovato", nameof(userId));
                }
            }
            else
            {
                // Altrimenti usa l'utente corrente
                targetUser = await _currentUserService.GetCurrentUserAsync();
                if (targetUser == null)
                {
                    throw new UnauthorizedAccessException("Impossibile determinare l'utente corrente");
                }
                targetUserId = targetUser.Id;
            }

            // Converti la stringa in Action
            var actionObj = ConvertToAction(action);

            // Se sono admin posso eseguire tutte le azioni
            if (targetUser.HasPermission(Permissions.Flows.AllowAll))
            {
                // Verifica se l'azione esiste per lo status corrente
                var actionsForStatus = FlowPermissionMatrix.ActionsByStatusAndRole.ContainsKey(flow.FlowStatus)
                    ? FlowPermissionMatrix.ActionsByStatusAndRole[flow.FlowStatus]
                    : new Dictionary<Action, FlowUserRoleType[]>();

                var actionExists = actionsForStatus.ContainsKey(actionObj);

                return new CanExecuteActionDto
                {
                    FlowId = flowId,
                    UserId = targetUserId,
                    Action = action,
                    FlowStatus = flow.FlowStatus,
                    UserRoles = new FlowUserRoleType[0], // Admin bypass dei ruoli specifici
                    CanExecute = actionExists,
                    AuthorizedRoles = actionExists ? FlowPermissionMatrix.GetAuthorizedRolesForAction(flow.FlowStatus, actionObj) : new FlowUserRoleType[0],
                    Reason = actionExists ? "Utente con permessi amministrativi" : "Azione non disponibile per lo status corrente del flusso"
                };
            }

            // Ottieni i sottoruoli dell'utente per questo flusso
            var userRoles = await _flowUserRepository.GetByFlowIdAsync(flowId, trackChanges: false);
            var userFlowRoles = userRoles.Where(ur => ur.UserId == targetUserId && !ur.IsDeleted).ToList();

            if (!userFlowRoles.Any())
            {
                return new CanExecuteActionDto
                {
                    FlowId = flowId,
                    UserId = targetUserId,
                    Action = action,
                    FlowStatus = flow.FlowStatus,
                    UserRoles = new FlowUserRoleType[0],
                    CanExecute = false,
                    AuthorizedRoles = FlowPermissionMatrix.GetAuthorizedRolesForAction(flow.FlowStatus, actionObj),
                    Reason = "L'utente non ha ruoli assegnati per questo flusso"
                };
            }

            // Verifica se almeno uno dei ruoli dell'utente può eseguire l'azione
            bool canExecute = userFlowRoles.Any(userRole => 
                FlowPermissionMatrix.CanExecuteAction(flow.FlowStatus, userRole.Role, actionObj));

            return new CanExecuteActionDto
            {
                FlowId = flowId,
                UserId = targetUserId,
                Action = action,
                FlowStatus = flow.FlowStatus,
                UserRoles = userFlowRoles.Select(ur => ur.Role).ToArray(),
                CanExecute = canExecute,
                AuthorizedRoles = FlowPermissionMatrix.GetAuthorizedRolesForAction(flow.FlowStatus, actionObj),
                Reason = canExecute ? null : "L'utente non ha i permessi necessari per eseguire questa azione"
            };
        }
    }
} 