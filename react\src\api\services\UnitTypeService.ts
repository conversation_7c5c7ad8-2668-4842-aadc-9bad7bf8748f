import { UnitTypeCreateDto, UnitTypeDto, UnitTypeEditCommandRolesDto } from '../models';
import { UnitTypeClient } from '../proxy';
import { httpClient } from '../httpClient';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://localhost:7079';

/**
 * Service wrapper per UnitTypeClient
 * Fornisce metodi statici per interagire con le API
 * Generato automaticamente - Non modificare manualmente
 */
export class UnitTypeService {
  private static unitTypeClient = new UnitTypeClient(
    API_BASE_URL,
    httpClient()
  );

  /**
   * getAll - Generato automaticamente
   * @returns Promise che risolve in UnitTypeDto[]
   */
  static getAll(signal?: AbortSignal): Promise<UnitTypeDto[]> {
    return this.unitTypeClient.getAll(signal);
  }

  /**
   * create - Generato automaticamente
   * @returns Promise che risolve in UnitTypeDto
   */
  static create(body: UnitTypeCreateDto | undefined, signal?: AbortSignal): Promise<UnitTypeDto> {
    return this.unitTypeClient.create(body, signal);
  }

  /**
   * getById - Generato automaticamente
   * @returns Promise che risolve in UnitTypeDto
   */
  static getById(id: string, signal?: AbortSignal): Promise<UnitTypeDto> {
    return this.unitTypeClient.getById(id, signal);
  }

  /**
   * update - Generato automaticamente
   * @returns Promise che risolve in void
   */
  static update(id: string, body: UnitTypeCreateDto | undefined, signal?: AbortSignal): Promise<void> {
    return this.unitTypeClient.update(id, body, signal);
  }

  /**
   * delete - Generato automaticamente
   * @returns Promise che risolve in void
   */
  static delete(id: string, signal?: AbortSignal): Promise<void> {
    return this.unitTypeClient.delete(id, signal);
  }

  /**
   * getAllWithCommandRoles - Generato automaticamente
   * @returns Promise che risolve in UnitTypeDto[]
   */
  static getAllWithCommandRoles(signal?: AbortSignal): Promise<UnitTypeDto[]> {
    return this.unitTypeClient.getAllWithCommandRoles(signal);
  }

  /**
   * updateCommandRoles - Generato automaticamente
   * @returns Promise che risolve in void
   */
  static updateCommandRoles(body: UnitTypeEditCommandRolesDto | undefined, signal?: AbortSignal): Promise<void> {
    return this.unitTypeClient.updateCommandRoles(body, signal);
  }
}
