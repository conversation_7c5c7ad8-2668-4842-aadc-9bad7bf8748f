using Microsoft.EntityFrameworkCore;
using S2M.DomainModelLayer.Entities;
using S2M.DomainModelLayer.Enums;
using S2M.InfrastructureLayer.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace S2M.InfrastructureLayer.Repository
{
    /// <summary>
    /// Implementazione del repository per i ruoli delle unità organizzative nei progetti
    /// </summary>
    public class ProjectOrganizationUnitRoleRepository : FullAuditedRepository<ProjectOrganizationUnitRole>, IProjectOrganizationUnitRoleRepository
    {
        public ProjectOrganizationUnitRoleRepository(S2MDbContext context, IIdentityUser identityUser) : base(context, identityUser)
        {
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<ProjectOrganizationUnitRole>> GetByProjectIdAsync(Guid projectId, bool trackChanges = false)
        {
            IQueryable<ProjectOrganizationUnitRole> query = _dbSet
                .Include(r => r.OrganizationUnit)
                .Include(r => r.Project)
                .Where(r => r.ProjectId == projectId && !r.IsDeleted);

            if (!trackChanges)
                query = query.AsNoTracking();

            return await query.ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<ProjectOrganizationUnitRole>> GetByOrganizationUnitIdAsync(Guid organizationUnitId, bool trackChanges = false)
        {
            IQueryable<ProjectOrganizationUnitRole> query = _dbSet
                .Include(r => r.OrganizationUnit)
                .Include(r => r.Project)
                .Where(r => r.OrganizationUnitId == organizationUnitId && !r.IsDeleted);

            if (!trackChanges)
                query = query.AsNoTracking();

            return await query.ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<ProjectOrganizationUnitRole> GetByProjectAndOrganizationUnitAsync(Guid projectId, Guid organizationUnitId, bool trackChanges = false)
        {
            IQueryable<ProjectOrganizationUnitRole> query = _dbSet
                .Include(r => r.OrganizationUnit)
                .Include(r => r.Project)
                .Where(r => r.ProjectId == projectId && r.OrganizationUnitId == organizationUnitId && !r.IsDeleted);

            if (!trackChanges)
                query = query.AsNoTracking();

            return await query.FirstOrDefaultAsync();
        }

        /// <inheritdoc/>
        public async Task<ProjectOrganizationUnitRole> GetOwnerByProjectIdAsync(Guid projectId, bool trackChanges = false)
        {
            IQueryable<ProjectOrganizationUnitRole> query = _dbSet
                .Include(r => r.OrganizationUnit)
                .Include(r => r.Project)
                .Where(r => r.ProjectId == projectId && r.UnitRole == ProjectOrganizationUnitRoleType.Owner && !r.IsDeleted);

            if (!trackChanges)
                query = query.AsNoTracking();

            return await query.FirstOrDefaultAsync();
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<ProjectOrganizationUnitRole>> GetCollaboratorsByProjectIdAsync(Guid projectId, bool trackChanges = false)
        {
            IQueryable<ProjectOrganizationUnitRole> query = _dbSet
                .Include(r => r.OrganizationUnit)
                .Include(r => r.Project)
                .Where(r => r.ProjectId == projectId && r.UnitRole == ProjectOrganizationUnitRoleType.Collaborator && !r.IsDeleted);

            if (!trackChanges)
                query = query.AsNoTracking();

            return await query.ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<int> RemoveByProjectIdAsync(Guid projectId)
        {
            var roles = await _dbSet
                .Where(r => r.ProjectId == projectId && !r.IsDeleted)
                .ToListAsync();

            foreach (var role in roles)
            {
                role.IsDeleted = true;
                role.DeletionTime = DateTime.UtcNow;
            }

            await _context.SaveChangesAsync();
            return roles.Count;
        }

        /// <inheritdoc/>
        public async Task<bool> RemoveByProjectAndOrganizationUnitAsync(Guid projectId, Guid organizationUnitId)
        {
            var role = await _dbSet
                .Where(r => r.ProjectId == projectId && r.OrganizationUnitId == organizationUnitId && !r.IsDeleted)
                .FirstOrDefaultAsync();

            if (role == null)
                return false;

            role.IsDeleted = true;
            role.DeletionTime = DateTime.UtcNow;
            await _context.SaveChangesAsync();
            return true;
        }
    }
} 