using Microsoft.AspNetCore.Mvc;
using S2M.ApplicationLayer.Dto;
using S2M.ApplicationLayer.Services.Interfaces;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace S2M_API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SheetController : ControllerBase
    {
        private readonly ISheetService _sheetService;

        public SheetController(ISheetService sheetService)
        {
            _sheetService = sheetService;
        }

        /// <summary>
        /// Ottiene tutti gli sheet
        /// </summary>
        /// <returns>Lista di sheet</returns>
        [HttpGet]
        public async Task<ActionResult<IEnumerable<SheetDto>>> GetAll()
        {
            var sheets = await _sheetService.GetAllAsync();
            return Ok(sheets);
        }

        /// <summary>
        /// Ottiene uno sheet per ID
        /// </summary>
        /// <param name="id">ID dello sheet</param>
        /// <returns>Sheet</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<SheetDto>> GetById(Guid id)
        {
            var sheet = await _sheetService.GetByIdAsync(id);
            if (sheet == null)
            {
                return NotFound();
            }
            return Ok(sheet);
        }

        /// <summary>
        /// Ottiene gli sheet di un flusso
        /// </summary>
        /// <param name="flowId">ID del flusso</param>
        /// <returns>Lista degli sheet del flusso</returns>
        [HttpGet("flow/{flowId}")]
        public async Task<ActionResult<IEnumerable<SheetDto>>> GetByFlowId(Guid flowId)
        {
            var sheets = await _sheetService.GetByFlowIdAsync(flowId);
            return Ok(sheets);
        }

        /// <summary>
        /// Ottiene solo il contenuto di uno sheet
        /// </summary>
        /// <param name="id">ID dello sheet</param>
        /// <returns>Contenuto HTML dello sheet</returns>
        [HttpGet("{id}/content")]
        public async Task<ActionResult<string>> GetContent(Guid id)
        {
            var content = await _sheetService.GetContentAsync(id);
            if (content == null)
            {
                return NotFound();
            }
            return Ok(content);
        }

        /// <summary>
        /// Crea un nuovo sheet
        /// </summary>
        /// <param name="sheetCreateDto">Dati del sheet</param>
        /// <returns>Sheet creato</returns>
        [HttpPost]
        public async Task<ActionResult<SheetDto>> Create(SheetCreateDto sheetCreateDto)
        {
            try
            {
                var createdSheet = await _sheetService.CreateAsync(sheetCreateDto);
                return CreatedAtAction(nameof(GetById), new { id = createdSheet.Id }, createdSheet);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Aggiorna uno sheet
        /// </summary>
        /// <param name="id">ID dello sheet</param>
        /// <param name="sheetDto">Dati aggiornati dello sheet</param>
        /// <returns>Nessun contenuto</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> Update(Guid id, SheetDto sheetDto)
        {
            if (id != sheetDto.Id)
            {
                return BadRequest();
            }

            try
            {
                var result = await _sheetService.UpdateAsync(id, sheetDto);
                if (!result)
                {
                    return NotFound();
                }
                return NoContent();
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Elimina uno sheet
        /// </summary>
        /// <param name="id">ID dello sheet</param>
        /// <returns>Nessun contenuto</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(Guid id)
        {
            var result = await _sheetService.DeleteAsync(id);
            if (!result)
            {
                return NotFound();
            }
            return NoContent();
        }

        /// <summary>
        /// Aggiorna il contenuto di uno sheet e i suoi commenti in una singola transazione
        /// </summary>
        /// <param name="id">ID dello sheet</param>
        /// <param name="updateDto">DTO con contenuto e operazioni sui commenti</param>
        /// <returns>Risultato dell'operazione</returns>
        [HttpPut("{id}/content-with-comments")]
        public async Task<ActionResult<SheetUpdateWithCommentsResponseDto>> UpdateContentWithComments(Guid id, SheetUpdateWithCommentsDto updateDto)
        {
            try
            {
                var result = await _sheetService.UpdateContentWithCommentsAsync(id, updateDto);
                
                if (!result.Success)
                {
                    return BadRequest(result.ErrorMessage);
                }
                
                return Ok(result);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (Exception ex)
            {
                return StatusCode(500, $"Errore interno del server: {ex.Message}");
            }
        }

        /// <summary>
        /// Crea un commento e aggiorna automaticamente il contenuto dello sheet
        /// </summary>
        /// <param name="id">ID dello sheet</param>
        /// <param name="request">Richiesta con dati del commento e contenuto</param>
        /// <returns>Commento creato</returns>
        [HttpPost("{id}/comment-with-content-update")]
        public async Task<ActionResult<SheetCommentDto>> CreateCommentWithContentUpdate(Guid id, CreateCommentWithContentUpdateRequest request)
        {
            try
            {
                var createdComment = await _sheetService.CreateCommentWithContentUpdateAsync(
                    id, 
                    request.CommentDto, 
                    request.CurrentContent
                );
                return Ok(createdComment);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Elimina un commento e aggiorna automaticamente il contenuto dello sheet
        /// </summary>
        /// <param name="id">ID dello sheet</param>
        /// <param name="commentId">ID del commento da eliminare</param>
        /// <param name="request">Richiesta con il contenuto corrente</param>
        /// <returns>Risultato dell'operazione</returns>
        [HttpDelete("{id}/comment/{commentId}/with-content-update")]
        public async Task<IActionResult> DeleteCommentWithContentUpdate(Guid id, Guid commentId, DeleteCommentWithContentUpdateRequest request)
        {
            try
            {
                var result = await _sheetService.DeleteCommentWithContentUpdateAsync(id, commentId, request.CurrentContent);
                
                if (!result)
                {
                    return NotFound("Sheet o commento non trovato");
                }
                
                return NoContent();
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                return BadRequest(ex.Message);
            }
        }
    }

    /// <summary>
    /// Richiesta per creare un commento con aggiornamento del contenuto
    /// </summary>
    public class CreateCommentWithContentUpdateRequest
    {
        /// <summary>
        /// Dati del commento da creare
        /// </summary>
        public SheetCommentCreateDto CommentDto { get; set; }

        /// <summary>
        /// Contenuto HTML corrente dello sheet
        /// </summary>
        public string CurrentContent { get; set; }
    }

    /// <summary>
    /// Richiesta per eliminare un commento con aggiornamento del contenuto
    /// </summary>
    public class DeleteCommentWithContentUpdateRequest
    {
        public string CurrentContent { get; set; }
    }
} 