using Microsoft.AspNetCore.Mvc;
using S2M.ApplicationLayer.Dto;
using S2M.ApplicationLayer.Services.Interfaces;
using S2M.DomainModelLayer.Enums;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace S2M_API.Controllers
{
    [ApiController]
    [Route("api/[controller]")]
    public class SheetCommentController : ControllerBase
    {
        private readonly ISheetCommentService _sheetCommentService;

        public SheetCommentController(ISheetCommentService sheetCommentService)
        {
            _sheetCommentService = sheetCommentService;
        }

        /// <summary>
        /// Ottiene tutti i commenti di un foglio
        /// </summary>
        /// <param name="sheetId">ID del foglio</param>
        /// <param name="currentFlowStatus">Stato corrente del flusso per filtrare i commenti visibili</param>
        /// <returns>Lista di commenti del foglio</returns>
        [HttpGet("sheet/{sheetId}")]
        public async Task<ActionResult<IEnumerable<SheetCommentDto>>> GetBySheetId(Guid sheetId, [FromQuery] FlowStatus? currentFlowStatus = null)
        {
            var comments = await _sheetCommentService.GetBySheetIdAsync(sheetId, currentFlowStatus);
            return Ok(comments);
        }

        /// <summary>
        /// Ottiene tutti i commenti di un foglio con un RefId specifico
        /// </summary>
        /// <param name="sheetId">ID del foglio</param>
        /// <param name="refId">Riferimento HTML dell'elemento</param>
        /// <param name="currentFlowStatus">Stato corrente del flusso per filtrare i commenti visibili</param>
        /// <returns>Lista di commenti per l'elemento specifico</returns>
        [HttpGet("sheet/{sheetId}/ref/{refId}")]
        public async Task<ActionResult<IEnumerable<SheetCommentDto>>> GetBySheetIdAndRefId(Guid sheetId, string refId, [FromQuery] FlowStatus? currentFlowStatus = null)
        {
            var comments = await _sheetCommentService.GetBySheetIdAndRefIdAsync(sheetId, refId, currentFlowStatus);
            return Ok(comments);
        }

        /// <summary>
        /// Ottiene i commenti principali (senza padre) di un foglio con le loro risposte
        /// </summary>
        /// <param name="sheetId">ID del foglio</param>
        /// <param name="currentFlowStatus">Stato corrente del flusso per filtrare i commenti visibili</param>
        /// <returns>Lista di commenti principali con risposte</returns>
        [HttpGet("sheet/{sheetId}/main")]
        public async Task<ActionResult<IEnumerable<SheetCommentDto>>> GetMainCommentsWithReplies(Guid sheetId, [FromQuery] FlowStatus? currentFlowStatus = null)
        {
            var comments = await _sheetCommentService.GetMainCommentsWithRepliesAsync(sheetId, currentFlowStatus);
            return Ok(comments);
        }

        /// <summary>
        /// Ottiene un commento per ID
        /// </summary>
        /// <param name="id">ID del commento</param>
        /// <returns>Commento</returns>
        [HttpGet("{id}")]
        public async Task<ActionResult<SheetCommentDto>> GetById(Guid id)
        {
            var comment = await _sheetCommentService.GetByIdAsync(id);
            if (comment == null)
            {
                return NotFound();
            }

            return Ok(comment);
        }

        /// <summary>
        /// Ottiene un commento con tutte le sue risposte
        /// </summary>
        /// <param name="commentId">ID del commento</param>
        /// <returns>Commento con le risposte</returns>
        [HttpGet("{commentId}/with-replies")]
        public async Task<ActionResult<SheetCommentDto>> GetWithReplies(Guid commentId)
        {
            var comment = await _sheetCommentService.GetWithRepliesAsync(commentId);
            if (comment == null)
            {
                return NotFound();
            }

            return Ok(comment);
        }

        /// <summary>
        /// Crea un nuovo commento
        /// </summary>
        /// <param name="commentDto">Dati del commento</param>
        /// <returns>Commento creato</returns>
        [HttpPost]
        public async Task<ActionResult<SheetCommentDto>> Create(SheetCommentCreateDto commentDto)
        {
            try
            {
                var createdComment = await _sheetCommentService.CreateAsync(commentDto);
                return CreatedAtAction(nameof(GetById), new { id = createdComment.Id }, createdComment);
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Aggiorna un commento esistente
        /// </summary>
        /// <param name="id">ID del commento</param>
        /// <param name="commentDto">Dati aggiornati del commento</param>
        /// <returns>Nessun contenuto</returns>
        [HttpPut("{id}")]
        public async Task<IActionResult> Update(Guid id, SheetCommentUpdateDto commentDto)
        {
            if (id != commentDto.Id)
            {
                return BadRequest("L'ID nel percorso non corrisponde all'ID nel corpo della richiesta");
            }

            try
            {
                var result = await _sheetCommentService.UpdateAsync(commentDto);
                if (!result)
                {
                    return NotFound();
                }

                return NoContent();
            }
            catch (ArgumentException ex)
            {
                return BadRequest(ex.Message);
            }
        }

        /// <summary>
        /// Elimina un commento
        /// </summary>
        /// <param name="id">ID del commento</param>
        /// <returns>Nessun contenuto</returns>
        [HttpDelete("{id}")]
        public async Task<IActionResult> Delete(Guid id)
        {
            var result = await _sheetCommentService.DeleteAsync(id);
            if (!result)
            {
                return NotFound();
            }

            return NoContent();
        }

        /// <summary>
        /// Verifica se un commento ha risposte
        /// </summary>
        /// <param name="commentId">ID del commento</param>
        /// <returns>True se ha risposte, False altrimenti</returns>
        [HttpGet("{commentId}/has-replies")]
        public async Task<ActionResult<bool>> HasReplies(Guid commentId)
        {
            var hasReplies = await _sheetCommentService.HasRepliesAsync(commentId);
            return Ok(hasReplies);
        }
    }
} 