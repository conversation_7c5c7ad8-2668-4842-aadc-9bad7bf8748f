using S2M.DomainModelLayer.Entities;
using S2M.DomainModelLayer.Enums;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace S2M.InfrastructureLayer.Repository
{
    /// <summary>
    /// Interfaccia per il repository dei ruoli delle unità organizzative nei progetti
    /// </summary>
    public interface IProjectOrganizationUnitRoleRepository : IRepository<ProjectOrganizationUnitRole>
    {
        /// <summary>
        /// Ottiene tutti i ruoli per un progetto specifico
        /// </summary>
        /// <param name="projectId">ID del progetto</param>
        /// <param name="trackChanges">Se tracciare le modifiche</param>
        /// <returns>Lista dei ruoli</returns>
        Task<IEnumerable<ProjectOrganizationUnitRole>> GetByProjectIdAsync(Guid projectId, bool trackChanges = false);

        /// <summary>
        /// Ottiene tutti i ruoli per un'unità organizzativa specifica
        /// </summary>
        /// <param name="organizationUnitId">ID dell'unità organizzativa</param>
        /// <param name="trackChanges">Se tracciare le modifiche</param>
        /// <returns>Lista dei ruoli</returns>
        Task<IEnumerable<ProjectOrganizationUnitRole>> GetByOrganizationUnitIdAsync(Guid organizationUnitId, bool trackChanges = false);

        /// <summary>
        /// Ottiene un ruolo specifico per progetto e unità organizzativa
        /// </summary>
        /// <param name="projectId">ID del progetto</param>
        /// <param name="organizationUnitId">ID dell'unità organizzativa</param>
        /// <param name="trackChanges">Se tracciare le modifiche</param>
        /// <returns>Ruolo se trovato</returns>
        Task<ProjectOrganizationUnitRole> GetByProjectAndOrganizationUnitAsync(Guid projectId, Guid organizationUnitId, bool trackChanges = false);

        /// <summary>
        /// Ottiene l'owner di un progetto
        /// </summary>
        /// <param name="projectId">ID del progetto</param>
        /// <param name="trackChanges">Se tracciare le modifiche</param>
        /// <returns>Owner del progetto</returns>
        Task<ProjectOrganizationUnitRole> GetOwnerByProjectIdAsync(Guid projectId, bool trackChanges = false);

        /// <summary>
        /// Ottiene tutti i collaboratori di un progetto
        /// </summary>
        /// <param name="projectId">ID del progetto</param>
        /// <param name="trackChanges">Se tracciare le modifiche</param>
        /// <returns>Lista dei collaboratori</returns>
        Task<IEnumerable<ProjectOrganizationUnitRole>> GetCollaboratorsByProjectIdAsync(Guid projectId, bool trackChanges = false);

        /// <summary>
        /// Rimuove tutti i ruoli per un progetto
        /// </summary>
        /// <param name="projectId">ID del progetto</param>
        /// <returns>Numero di ruoli rimossi</returns>
        Task<int> RemoveByProjectIdAsync(Guid projectId);

        /// <summary>
        /// Rimuove un ruolo specifico per progetto e unità organizzativa
        /// </summary>
        /// <param name="projectId">ID del progetto</param>
        /// <param name="organizationUnitId">ID dell'unità organizzativa</param>
        /// <returns>True se rimosso, false se non trovato</returns>
        Task<bool> RemoveByProjectAndOrganizationUnitAsync(Guid projectId, Guid organizationUnitId);
    }
} 