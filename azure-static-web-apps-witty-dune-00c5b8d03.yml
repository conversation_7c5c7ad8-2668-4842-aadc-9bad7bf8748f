name: Azure Static Web Apps CI/CD

pr:
  branches:
    include:
      - certification
trigger:
  branches:
    include:
      - certification
  paths:
    include:
      - react/*

jobs:
- job: build_and_deploy_job
  displayName: Build and Deploy Job
  condition: or(eq(variables['Build.Reason'], 'Manual'),or(eq(variables['Build.Reason'], 'PullRequest'),eq(variables['Build.Reason'], 'IndividualCI')))
  pool:
    vmImage: ubuntu-latest
  variables:
  - group: Azure-Static-Web-Apps-witty-dune-00c5b8d03-variable-group
  steps:
  - checkout: self
    submodules: true
  - task: AzureStaticWebApp@0
    inputs:
      azure_static_web_apps_api_token: $(AZURE_STATIC_WEB_APPS_API_TOKEN_WITTY_DUNE_00C5B8D03)
###### Repository/Build Configurations - These values can be configured to match your app requirements. ######
# For more information regarding Static Web App workflow configurations, please visit: https://aka.ms/swaworkflowconfig
      app_location: "react" # App source code path
      api_location: "" # Api source code path - optional
      output_location: "dist" # Built app content directory - optional
###### End of Repository/Build Configurations ######

