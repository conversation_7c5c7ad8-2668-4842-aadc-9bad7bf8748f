{
  "$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentTemplate.json#",
  "contentVersion": "1.0.0.0",
  "parameters": {
    "location": {
      "type": "string",
      "defaultValue": "westeurope"
    },
    "appName": {
      "type": "string",
      "defaultValue": "s2m-api-app"
    },
    "serverFarmName": {
      "type": "string",
      "defaultValue": "s2m-api-plan"
    },
    "image": {
      "type": "string",
      "defaultValue": "cvecr.azurecr.io/s2m-api:latest"
    }
  },
  "resources": [
    {
      "type": "Microsoft.Web/serverfarms",
      "apiVersion": "2022-03-01",
      "name": "[parameters('serverFarmName')]",
      "location": "[parameters('location')]",
      "sku": {
        "name": "B1",
        "tier": "Basic",
        "size": "B1",
        "family": "B",
        "capacity": 1
      },
      "properties": {
        "name": "[parameters('serverFarmName')]",
        "reserved": true // Linux
      }
    },
    {
      "type": "Microsoft.Web/sites",
      "apiVersion": "2022-03-01",
      "name": "[parameters('appName')]",
      "location": "[parameters('location')]",
      "dependsOn": [
        "[resourceId('Microsoft.Web/serverfarms', parameters('serverFarmName'))]"
      ],
      "properties": {
        "serverFarmId": "[resourceId('Microsoft.Web/serverfarms', parameters('serverFarmName'))]",
        "siteConfig": {
          "linuxFxVersion": "[concat('DOCKER|', parameters('image'))]",
          "appSettings": [
            {
              "name": "WEBSITES_PORT",
              "value": "80"
            },
            {
              "name": "ASPNETCORE_ENVIRONMENT",
              "value": "Production"
            },
            {
              "name": "ConnectionStrings__DefaultConnection",
              "value": "Host=s2mdbserver.postgres.database.azure.com;Database=S2MDb;Username=postgresql;Password=***********;Ssl Mode=Require"
            },
            {
              "name": "CorsOrigins__0",
              "value": "https://witty-dune-00c5b8d03.6.azurestaticapps.net"
            }
          ]
        },
        "httpsOnly": true
      }
    }
  ]
}
