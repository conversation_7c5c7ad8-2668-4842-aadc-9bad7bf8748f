using Microsoft.EntityFrameworkCore;
using S2M.DomainModelLayer.Entities;
using S2M.InfrastructureLayer.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace S2M.InfrastructureLayer.Repository
{
    /// <summary>
    /// Implementazione del repository per l'entità SheetComment
    /// </summary>
    public class SheetCommentRepository : FullAuditedRepository<SheetComment>, ISheetCommentRepository
    {
        public SheetCommentRepository(S2MDbContext context, IIdentityUser identityUser) : base(context, identityUser)
        {
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<SheetComment>> GetBySheetIdAsync(Guid sheetId)
        {
            return await _dbSet
                .Include(c => c.ParentComment)
                .Include(c => c.Replies)
                .Where(c => c.SheetId == sheetId && !c.IsDeleted)
                .OrderBy(c => c.CreationTime)
                .AsNoTracking()
                .ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<SheetComment>> GetBySheetIdAndRefIdAsync(Guid sheetId, string refId)
        {
            return await _dbSet
                .Include(c => c.ParentComment)
                .Include(c => c.Replies)
                .Where(c => c.SheetId == sheetId && c.RefId == refId && !c.IsDeleted)
                .OrderBy(c => c.CreationTime)
                .AsNoTracking()
                .ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<SheetComment>> GetRepliesAsync(Guid parentCommentId)
        {
            return await _dbSet
                .Include(c => c.ParentComment)
                .Include(c => c.Replies)
                .Where(c => c.ParentCommentId == parentCommentId && !c.IsDeleted)
                .OrderBy(c => c.CreationTime)
                .AsNoTracking()
                .ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<IEnumerable<SheetComment>> GetMainCommentsAsync(Guid sheetId)
        {
            return await _dbSet
                .Include(c => c.Replies.Where(r => !r.IsDeleted))
                .Where(c => c.SheetId == sheetId && c.ParentCommentId == null && !c.IsDeleted)
                .OrderBy(c => c.CreationTime)
                .AsNoTracking()
                .ToListAsync();
        }

        /// <inheritdoc/>
        public async Task<SheetComment> GetWithRepliesAsync(Guid commentId)
        {
            return await _dbSet
                .Include(c => c.Replies.Where(r => !r.IsDeleted))
                    .ThenInclude(r => r.Replies.Where(rr => !rr.IsDeleted))
                .Where(c => c.Id == commentId && !c.IsDeleted)
                .AsNoTracking()
                .FirstOrDefaultAsync();
        }

        /// <inheritdoc/>
        public async Task<bool> HasRepliesAsync(Guid commentId)
        {
            return await _dbSet
                .AnyAsync(c => c.ParentCommentId == commentId && !c.IsDeleted);
        }
    }
} 