using S2M.DomainModelLayer.Entities;
using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace S2M.InfrastructureLayer.Repository
{
    /// <summary>
    /// Interfaccia per il repository dei commenti sui fogli
    /// </summary>
    public interface ISheetCommentRepository : IRepository<SheetComment>
    {
        /// <summary>
        /// Ottiene tutti i commenti di un foglio specifico
        /// </summary>
        /// <param name="sheetId">ID del foglio</param>
        /// <returns>Lista di commenti del foglio</returns>
        Task<IEnumerable<SheetComment>> GetBySheetIdAsync(Guid sheetId);

        /// <summary>
        /// Ottiene tutti i commenti di un foglio con un RefId specifico
        /// </summary>
        /// <param name="sheetId">ID del foglio</param>
        /// <param name="refId">Riferimento HTML dell'elemento</param>
        /// <returns>Lista di commenti per l'elemento specifico</returns>
        Task<IEnumerable<SheetComment>> GetBySheetIdAndRefIdAsync(Guid sheetId, string refId);

        /// <summary>
        /// Ottiene le risposte di un commento specifico
        /// </summary>
        /// <param name="parentCommentId">ID del commento padre</param>
        /// <returns>Lista di risposte al commento</returns>
        Task<IEnumerable<SheetComment>> GetRepliesAsync(Guid parentCommentId);

        /// <summary>
        /// Ottiene i commenti principali (senza padre) di un foglio
        /// </summary>
        /// <param name="sheetId">ID del foglio</param>
        /// <returns>Lista di commenti principali del foglio</returns>
        Task<IEnumerable<SheetComment>> GetMainCommentsAsync(Guid sheetId);

        /// <summary>
        /// Ottiene un commento con tutte le sue risposte
        /// </summary>
        /// <param name="commentId">ID del commento</param>
        /// <returns>Commento con le risposte</returns>
        Task<SheetComment> GetWithRepliesAsync(Guid commentId);

        /// <summary>
        /// Verifica se un commento ha risposte
        /// </summary>
        /// <param name="commentId">ID del commento</param>
        /// <returns>True se ha risposte, False altrimenti</returns>
        Task<bool> HasRepliesAsync(Guid commentId);
    }
} 