import { useState, useEffect } from 'react';
import { FlowAvailableActionsDto } from '../api/models';
import { FlowService } from '../api/services/FlowService';

interface UseFlowPermissionsProps {
  flowId: string;
  userId?: string;
}

interface UseFlowPermissionsReturn {
  availableActions: string[];
  loading: boolean;
  error: string | null;
  hasAction: (action: string) => boolean;
  refresh: () => Promise<void>;
}

/**
 * Hook personalizzato per gestire i permessi del flusso
 * Carica le azioni disponibili per l'utente corrente e fornisce helper per verificare i permessi
 */
export const useFlowPermissions = ({ 
  flowId, 
  userId 
}: UseFlowPermissionsProps): UseFlowPermissionsReturn => {
  const [permissionsData, setPermissionsData] = useState<FlowAvailableActionsDto | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const loadPermissions = async () => {
    if (!flowId) return;

    try {
      setLoading(true);
      setError(null);
      
      const result = await FlowService.getAvailableActions(flowId, userId);
      setPermissionsData(result);
    } catch (err) {
      console.error('Errore nel caricamento dei permessi del flusso:', err);
      setError('Errore nel caricamento dei permessi');
      setPermissionsData(null);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadPermissions();
  }, [flowId, userId]);

  const hasAction = (action: string): boolean => {
    if (!permissionsData || !permissionsData.availableActions) {
      return false;
    }
    return permissionsData.availableActions.includes(action);
  };

  return {
    availableActions: permissionsData?.availableActions || [],
    loading,
    error,
    hasAction,
    refresh: loadPermissions
  };
}; 