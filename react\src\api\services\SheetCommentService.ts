import { SheetCommentCreateDto, SheetCommentDto, SheetCommentUpdateDto } from '../models';
import { FlowStatus } from '../enums';
import { SheetCommentClient } from '../proxy';
import { httpClient } from '../httpClient';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'https://localhost:7079';

/**
 * Service wrapper per SheetCommentClient
 * Fornisce metodi statici per interagire con le API
 * Generato automaticamente - Non modificare manualmente
 */
export class SheetCommentService {
  private static sheetCommentClient = new SheetCommentClient(
    API_BASE_URL,
    httpClient()
  );

  /**
   * getBySheetId - Generato automaticamente
   * @returns Promise che risolve in SheetCommentDto[]
   */
  static getBySheetId(sheetId: string, currentFlowStatus: FlowStatus | undefined, signal?: AbortSignal): Promise<SheetCommentDto[]> {
    return this.sheetCommentClient.getBySheetId(sheetId, currentFlowStatus, signal);
  }

  /**
   * getBySheetIdAndRefId - Generato automaticamente
   * @returns Promise che risolve in SheetCommentDto[]
   */
  static getBySheetIdAndRefId(sheetId: string, refId: string, currentFlowStatus: FlowStatus | undefined, signal?: AbortSignal): Promise<SheetCommentDto[]> {
    return this.sheetCommentClient.getBySheetIdAndRefId(sheetId, refId, currentFlowStatus, signal);
  }

  /**
   * getMainCommentsWithReplies - Generato automaticamente
   * @returns Promise che risolve in SheetCommentDto[]
   */
  static getMainCommentsWithReplies(sheetId: string, currentFlowStatus: FlowStatus | undefined, signal?: AbortSignal): Promise<SheetCommentDto[]> {
    return this.sheetCommentClient.getMainCommentsWithReplies(sheetId, currentFlowStatus, signal);
  }

  /**
   * getById - Generato automaticamente
   * @returns Promise che risolve in SheetCommentDto
   */
  static getById(id: string, signal?: AbortSignal): Promise<SheetCommentDto> {
    return this.sheetCommentClient.getById(id, signal);
  }

  /**
   * update - Generato automaticamente
   * @returns Promise che risolve in void
   */
  static update(id: string, body: SheetCommentUpdateDto | undefined, signal?: AbortSignal): Promise<void> {
    return this.sheetCommentClient.update(id, body, signal);
  }

  /**
   * delete - Generato automaticamente
   * @returns Promise che risolve in void
   */
  static delete(id: string, signal?: AbortSignal): Promise<void> {
    return this.sheetCommentClient.delete(id, signal);
  }

  /**
   * getWithReplies - Generato automaticamente
   * @returns Promise che risolve in SheetCommentDto
   */
  static getWithReplies(commentId: string, signal?: AbortSignal): Promise<SheetCommentDto> {
    return this.sheetCommentClient.getWithReplies(commentId, signal);
  }

  /**
   * create - Generato automaticamente
   * @returns Promise che risolve in SheetCommentDto
   */
  static create(body: SheetCommentCreateDto | undefined, signal?: AbortSignal): Promise<SheetCommentDto> {
    return this.sheetCommentClient.create(body, signal);
  }

  /**
   * hasReplies - Generato automaticamente
   * @returns Promise che risolve in boolean
   */
  static hasReplies(commentId: string, signal?: AbortSignal): Promise<boolean> {
    return this.sheetCommentClient.hasReplies(commentId, signal);
  }
}
