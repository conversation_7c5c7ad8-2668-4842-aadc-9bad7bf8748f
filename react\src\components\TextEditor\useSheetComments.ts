import { useState, useEffect, useCallback } from 'react';
import { SheetCommentService } from '../../api/services';
import { SheetCommentDto, SheetCommentCreateDto, SheetCommentUpdateDto, FlowStatus } from '../../api/proxy';

// Interfaccia per i parametri del hook
interface UseSheetCommentsProps {
  sheetId: string;
  flowStatus: FlowStatus;
}

// Interfaccia per il valore di ritorno del hook
interface UseSheetCommentsReturn {
  // Stati
  comments: SheetCommentDto[];
  loading: boolean;
  error: string | null;
  
  // Funzioni per gestire i commenti
  loadComments: () => Promise<void>;
  loadCommentsByRefId: (refId: string) => Promise<SheetCommentDto[]>;
  createComment: (refId: string, comment: string, parentCommentId?: string) => Promise<SheetCommentDto | null>;
  updateComment: (commentId: string, comment: string) => Promise<boolean>;
  deleteComment: (commentId: string) => Promise<boolean>;
  
  // Funzioni di utilità
  getCommentsByRefId: (refId: string) => SheetCommentDto[];
  getMainComments: () => SheetCommentDto[];
  clearError: () => void;
}

/**
 * Hook personalizzato per gestire i commenti sui sheet
 * @param params Parametri del hook (sheetId e flowStatus)
 * @returns Oggetto con stati e funzioni per gestire i commenti
 */
export const useSheetComments = ({ sheetId, flowStatus }: UseSheetCommentsProps): UseSheetCommentsReturn => {
  const [comments, setComments] = useState<SheetCommentDto[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);

  /**
   * Carica tutti i commenti del sheet
   */
  const loadComments = useCallback(async () => {
    if (!sheetId) return;

    setLoading(true);
    setError(null);

    try {
      const sheetComments = await SheetCommentService.getMainCommentsWithReplies(sheetId, flowStatus);
      setComments(sheetComments);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Errore durante il caricamento dei commenti');
      console.error('Errore nel caricamento dei commenti:', err);
    } finally {
      setLoading(false);
    }
  }, [sheetId]);

  /**
   * Carica i commenti per un RefId specifico
   */
  const loadCommentsByRefId = useCallback(async (refId: string): Promise<SheetCommentDto[]> => {
    if (!sheetId || !refId) return [];

    setLoading(true);
    setError(null);

    try {
      const refComments = await SheetCommentService.getBySheetIdAndRefId(sheetId, refId, flowStatus);
      return refComments;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Errore durante il caricamento dei commenti');
      console.error('Errore nel caricamento dei commenti per RefId:', err);
      return [];
    } finally {
      setLoading(false);
    }
  }, [sheetId]);

  /**
   * Crea un nuovo commento
   */
  const createComment = useCallback(async (
    refId: string, 
    comment: string, 
    parentCommentId?: string
  ): Promise<SheetCommentDto | null> => {
    if (!sheetId || !refId || !comment.trim()) return null;

    setLoading(true);
    setError(null);

    try {
      const commentDto = new SheetCommentCreateDto({
        sheetId,
        refId,
        comment: comment.trim(),
        parentCommentId,
        flowStatus
      });

      const newComment = await SheetCommentService.create(commentDto);
      
      // Aggiorna lo stato locale
      if (parentCommentId) {
        // È una risposta, aggiorna il commento padre
        setComments(prevComments => 
          prevComments.map(c => {
            if (c.id === parentCommentId) {
              const updatedComment = new SheetCommentDto({
                ...c,
                replies: [...(c.replies || []), newComment]
              });
              return updatedComment;
            }
            return c;
          })
        );
      } else {
        // È un nuovo commento principale
        setComments(prevComments => [...prevComments, newComment]);
      }

      return newComment;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Errore durante la creazione del commento');
      console.error('Errore nella creazione del commento:', err);
      return null;
    } finally {
      setLoading(false);
    }
  }, [sheetId, flowStatus]);

  /**
   * Aggiorna un commento esistente
   */
  const updateComment = useCallback(async (commentId: string, comment: string): Promise<boolean> => {
    if (!commentId || !comment.trim()) return false;

    setLoading(true);
    setError(null);

    try {
      const updateDto = new SheetCommentUpdateDto({
        id: commentId,
        comment: comment.trim(),
        flowStatus
      });

      await SheetCommentService.update(commentId, updateDto);
      
      // Aggiorna lo stato locale
      setComments(prevComments => 
        prevComments.map(c => {
          if (c.id === commentId) {
            return new SheetCommentDto({ ...c, comment: comment.trim() });
          }
          // Controlla anche le risposte
          if (c.replies) {
            return new SheetCommentDto({
              ...c,
              replies: c.replies.map(r => 
                r.id === commentId ? new SheetCommentDto({ ...r, comment: comment.trim() }) : r
              )
            });
          }
          return c;
        })
      );

      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Errore durante l\'aggiornamento del commento');
      console.error('Errore nell\'aggiornamento del commento:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, [flowStatus]);

  /**
   * Elimina un commento
   */
  const deleteComment = useCallback(async (commentId: string): Promise<boolean> => {
    if (!commentId) return false;

    setLoading(true);
    setError(null);

    try {
      await SheetCommentService.delete(commentId);
      
      // Aggiorna lo stato locale
      setComments(prevComments => 
        prevComments
          .filter(c => c.id !== commentId) // Rimuovi se è un commento principale
          .map(c => new SheetCommentDto({
            ...c,
            replies: c.replies?.filter(r => r.id !== commentId) || [] // Rimuovi se è una risposta
          }))
      );

      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Errore durante l\'eliminazione del commento');
      console.error('Errore nell\'eliminazione del commento:', err);
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  /**
   * Ottiene i commenti per un RefId specifico dallo stato locale
   */
  const getCommentsByRefId = useCallback((refId: string): SheetCommentDto[] => {
    return comments.filter(comment => comment.refId === refId);
  }, [comments]);

  /**
   * Ottiene i commenti principali (senza padre) dallo stato locale
   */
  const getMainComments = useCallback((): SheetCommentDto[] => {
    return comments.filter(comment => !comment.parentCommentId);
  }, [comments]);

  /**
   * Pulisce l'errore corrente
   */
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Carica i commenti all'inizializzazione
  useEffect(() => {
    loadComments();
  }, [loadComments]);

  return {
    // Stati
    comments,
    loading,
    error,
    
    // Funzioni
    loadComments,
    loadCommentsByRefId,
    createComment,
    updateComment,
    deleteComment,
    
    // Utilità
    getCommentsByRefId,
    getMainComments,
    clearError
  };
}; 